import 'dart:io';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/base/build_config.dart';
import 'package:XyyBeanSproutsFlutter/common/slivers/custom_sliver_persistent_header_delegate.dart';
import 'package:XyyBeanSproutsFlutter/main/data/user_auth_model.dart';
import 'package:XyyBeanSproutsFlutter/mine/data/mine_menu_model.dart';
import 'package:XyyBeanSproutsFlutter/mine/widget/mine_block_menu_widget.dart';
import 'package:XyyBeanSproutsFlutter/mine/widget/mine_entry_item_widget.dart';
import 'package:XyyBeanSproutsFlutter/mine/widget/mine_top_widget.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/permission/permission_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';

class MinePage extends BasePage {
  @override
  BaseState<StatefulWidget> initState() {
    return MinePageState();
  }
}

class MinePageState extends BaseState<MinePage> {
  List<MineMenuModel>? menuConfig;
  String imageHost = "";
  String? name;
  String? department;

  @override
  void onCreate() {
    super.onCreate();
    XYYContainer.bridgeCall("app_host").then((value) {
      imageHost = value["interface"];
      setState(() {});
    });
    requestMenuConfig();
    UserInfoUtil.getUserInfo().then((value) {
      setState(() {
        name = value?.realName;
        department = "${value?.department ?? ""}";
      });
    });
  }

  @override
  void onResume() {
    super.onResume();
    this.requestMenuConfig();
  }

  void requestMenuConfig() {
    if (menuConfig == null) {
      showLoadingDialog();
    }
    UserAuthManager.getRoleJSONString().then((roleJSON) {
      NetworkV2<MineMenuParentModel>(MineMenuParentModel())
          .requestDataV2("getFindMenu",
              parameters: {"role": roleJSON}, method: RequestMethod.GET)
          .then((result) {
        dismissLoadingDialog();
        if (mounted && result.isSuccess == true) {
          setState(() {
            menuConfig = result.getData()?.rows;
          });
        }
      });
    });
  }

  @override
  bool needKeepAlive() {
    return true;
  }

  @override
  Widget buildWidget(BuildContext context) {
    List<MineMenuModel> firstSectionList = [];
    List<MineMenuModel> secondSectionList = [];
    if (menuConfig != null) {
      menuConfig?.forEach((element) {
        if ((Platform.isAndroid && element.platform == 2) ||
            Platform.isIOS && element.platform == 1) {
          return;
        }
        switch (element.sectionId) {
          case 0:
            if (element.typeID == '2' && !Permission.isPermission('visitManageMine')) {
                break;
            }
            if (element.typeID == '3' && !Permission.isPermission('orderAllManage')) {
                break;
            }
            if (element.typeID == '4' && !Permission.isPermission('orderAllChargebacks')) {
                break;
            }
            if (element.typeID == '14' && !Permission.isPermission('customerFunnelMine')) {
                break;
            }
            if (element.typeID == '20' && !Permission.isPermission('teamMine')) {
                break;
            }
            if (element.typeID == '23' && !Permission.isPermission('superWorryApply')) {
                break;
            }
            firstSectionList.add(element);
            break;
          case 1:
            secondSectionList.add(element);
            break;
        }
      });
    }

    return Container(
      color: Color(0xFFF7F7F8),
      height: double.infinity,
      padding: EdgeInsets.only(bottom: 10),
      child: CustomScrollView(
        slivers: [
          SliverPersistentHeader(
            pinned: true,
            delegate: CustomSliverPersistentHeaderDelegate(
              minHeight: 156 * MediaQuery.of(context).size.width / 375,
              maxHeight: 156 * MediaQuery.of(context).size.width / 375,
              child: MineTopWidget(
                name: name,
                department: department,
              ),
            ),
          ),
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                if (index == 0) {
                  return MineBlockMenuWidget(
                    title: "常用工具",
                    itemModels: firstSectionList,
                    itemClick: handleClick,
                    imageHost: imageHost,
                  );
                } else if (index == 1) {
                  return MineBlockMenuWidget(
                    title: "其他事项",
                    itemModels: secondSectionList,
                    itemClick: handleClick,
                    imageHost: imageHost,
                  );
                } else {
                  return MineEntryItemWidget();
                }
              },
              childCount: 3,
            ),
          ),
        ],
      ),
    );
  }

  void handleClick(MineMenuModel model) {
    trackEvent(model.typeID);
    if (model.jumpUrl == null) {
      showToast("跳转路径为空");
      return;
    }
    switch (model.actionType) {
      case 1:
        // 路由直接跳转页面
        openUrl(model.jumpUrl);
        break;
      case 2:
        // 跳转pdc页面
        openPDCUrl(model.jumpUrl);
        break;
      case 3:
        // 打开普通h5
        openCommonH5Url(model.jumpUrl);
        break;
      case 4:
        // 打开荷叶h5
        openHYUrl(model.jumpUrl);
        break;
      default:
        showToast("暂不支持该actionType:${model.actionType}");
        break;
    }
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return null;
  }

  @override
  String getTitleName() {
    return "我的";
  }

  void openUrl(String jumpUrl) async {
    var userInfo = await UserInfoUtil.getUserInfo();
    if (userInfo == null || userInfo.roleType == null) {
      showToast("userInfo或roleType为空");
      return;
    }
    String targetUrl;
    switch (jumpUrl) {
      case "/GoodsManagement":
        targetUrl = "/GoodsManagement?roleType=${userInfo.roleType}";
        break;
      case "/customer_funnel":
        targetUrl = "/customer_funnel?oaId=${userInfo.sysUserId}";
        break;
      case "/coupons_list": // 优惠券
        targetUrl = getCouponUrl(userInfo);
        break;
      default:
        targetUrl = jumpUrl;
        break;
    }
    targetUrl = Uri.encodeFull(targetUrl);
    XYYContainer.open(targetUrl);
  }

  /// 获取优惠券跳转链接，如果为M级账号则跳转组织结构筛选，如果为BD则直接跳转优惠券列表
  String getCouponUrl(UserInfoData userInfo) {
    if (userInfo.roleType == UserInfoUtil.TYPE_BDM ||
        userInfo.roleType == UserInfoUtil.TYPE_GJR_BDM) {
      // BDM或跟进人BDM
      return "/coupon_group_select_page";
    } else {
      // BD
      return "/coupons_list";
    }
  }

  void openPDCUrl(String jumpUrl) async {
    var appHosts = await XYYContainer.bridgeCall("app_host");

    if (appHosts is Map) {
      String pdcInterface = appHosts['h5_host'] ?? "";
      var sourceType = await UserAuthManager.getSourceType();
      String pdcUrl = "$pdcInterface$jumpUrl?source=$sourceType";
      pdcUrl = Uri.encodeComponent(pdcUrl);
      String router = "xyy://crm-app.ybm100.com/crm/web_view?url=$pdcUrl";
      XYYContainer.open(router);
    }
  }

  void openCommonH5Url(String jumpUrl) {
    jumpUrl = Uri.encodeComponent(jumpUrl);
    String router = "xyy://crm-app.ybm100.com/crm/web_view?url=$jumpUrl";
    XYYContainer.open(router);
  }

  void openHYUrl(String jumpUrl) async {
    var userInfo = await UserInfoUtil.getUserInfo();
    if (userInfo == null) {
      showToast("userInfo为空");
      return;
    }
    var appHosts = await XYYContainer.bridgeCall("app_host");
    var sourceType = await UserAuthManager.getSourceType();
    jumpUrl = appHosts["hy_h5_host"] +
        jumpUrl +
        "?token=${userInfo.token}&odId=${userInfo.sysUserId}&source=${sourceType}";
    jumpUrl = Uri.encodeComponent(jumpUrl);
    String router = "xyy://crm-app.ybm100.com/crm/web_view?url=$jumpUrl";
    XYYContainer.open(router);
  }

  void trackEvent(typeID) {
    XYYContainer.bridgeCall('event_track',
        parameters: {"action_type": "mc-mineId-$typeID"});
  }
}
