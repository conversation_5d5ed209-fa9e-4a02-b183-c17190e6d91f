import 'dart:collection';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/toast/toast_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/message_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';

import 'base_error_widget.dart';

abstract class BaseFunction {
  State? _stateBaseFunction;
  BuildContext? contextBaseFunction;

  /// 其他页面状态
  PageStateWidget _pageStateWidget = PageStateWidget();

  void initBaseCommon(State state) {
    _stateBaseFunction = state;
    contextBaseFunction = state.context;
  }

  Widget getBaseView(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.white, //背景颜色，可自己变更
      child: Stack(
        children: <Widget>[
          _buildProviderWidget(context),
          getPageStateWidget(),
        ],
      ),
    );
  }

  void setPageState(PageState pageState,
      {String? imagePath, String? buttonText, VoidCallback? errorClick}) {
    _pageStateWidget.setPageState(pageState,
        imagePath: imagePath, buttonText: buttonText, errorClick: errorClick);
  }

  // Widget _getHolderLWidget() {
  //   return Container(
  //     width: 0,
  //     height: 0,
  //   );
  // }

  ///返回 状态管理组件
  _buildProviderWidget(BuildContext context) {
    var providers = getProvider();
    if (providers == null || providers.isEmpty) {
      return buildWidget(context);
    } else {
      return MultiProvider(providers: providers, child: buildWidget(context));
    }
  }

  ///暴露的错误页面方法，可以自己重写定制
  Widget getPageStateWidget() {
    return _pageStateWidget;
  }

  ///返回屏幕宽度
  double getScreenWidth() {
    return MediaQuery.of(contextBaseFunction!).size.width;
  }

  ///返回屏幕高度
  double getScreenHeight() {
    return MediaQuery.of(contextBaseFunction!).size.height;
  }

  String getWidgetName() {
    if (contextBaseFunction == null) {
      return "";
    }
    return _stateBaseFunction?.widget.runtimeType.toString() ?? "null";
  }

  void log(String content) {
    print("guan " + getWidgetName() + ",$hashCode------:" + content);
  }

  ///弹对话框
  void showMessageDialog(String? message,
      {String title = "提示", String negativeText = "确定", Function? callBack}) {
    if (contextBaseFunction != null) {
      if (message != null && message.isNotEmpty) {
        showDialog<Null>(
            context: contextBaseFunction!, //BuildContext对象
            barrierDismissible: false,
            builder: (BuildContext context) {
              return MessageDialog(
                title: title,
                negativeText: negativeText,
                message: message,
                onCloseEvent: () {
                  Navigator.pop(context);
                  if (callBack != null) {
                    callBack();
                  }
                },
              ); //调用对话框);
            });
      }
    }
  }

  ///弹对话框
  void showMessageDialog2(
      {String title = "提示",
      String? message,
      String negativeText = "取消",
      String positiveText = "确定",
      Function? callBack,
      Function? cancelCallBack}) {
    if (contextBaseFunction != null) {
      if (message != null && message.isNotEmpty) {
        showDialog<Null>(
            context: contextBaseFunction!, //BuildContext对象
            barrierDismissible: false,
            builder: (BuildContext context) {
              return MessageDialog(
                title: title,
                negativeText: negativeText,
                positiveText: positiveText,
                message: message,
                onPositivePressEvent: () {
                  Navigator.pop(context);
                  if (callBack != null) {
                    callBack();
                  }
                },
                onCloseEvent: () {
                  Navigator.pop(context);
                  if (cancelCallBack != null) {
                    cancelCallBack();
                  }
                },
              ); //调用对话框);
            });
      }
    }
  }

  ///弹对话框,isMask:是否阻止点击透传
  void showLoadingDialog({
    String? msg,
    bool isMask = true,
    bool dismissOnTap = true,
  }) {
    EasyLoading.show(
        status: msg,
        maskType: isMask ? EasyLoadingMaskType.clear : EasyLoadingMaskType.none,
        dismissOnTap: dismissOnTap);
  }

  void dismissLoadingDialog() {
    EasyLoading.dismiss();
  }

  /// toast
  void showToast(String msg, {ToastType type = ToastType.Normal}) {
    // ignore: unnecessary_null_comparison
    if (type == null) {
      type = ToastType.Normal;
    }
    XYYContainer.toastChannel.toast(msg, type: type);
  }

  void track(String actionType, {Map<String, String>? extras}) {
    var hashMap = HashMap<String, String>();
    hashMap['action_type'] = actionType;
    if (extras != null && extras.isNotEmpty) {
      hashMap.addAll(extras);
    }
    XYYContainer.bridgeCall('event_track', parameters: hashMap);
  }

  ///---------------------------------页面可以重写的方法----------------------------

  Widget? getBottomNavigationBar(BuildContext context) {
    return null;
  }

  /// 获取需要的Provider，BaseWidget会自动加载到顶层
  /// Provider使用参考:https://flutter.cn/docs/development/data-and-backend/state-mgmt/simple
  List<SingleChildWidget>? getProvider() {
    return null;
  }

  /// widget创建，只会调用一次
  void onCreate() {
    log("guan onCreate:${getWidgetName()}");
  }

  /// widget展示，与onPause()成对调用
  /// 子页面暂不支持
  void onResume() {
    log("guan onResume:${getWidgetName()}");
    track("PV-${getWidgetName()}");
  }

  /// widget消失，会多次调用，退后台
  /// warning!!!!!!    进入二级页不会调用该方法，等遇到实际场景再解决
  /// 子页面暂不支持
  void onPause() {
    log("onPause:${getWidgetName()}");
    track("PD-${getWidgetName()}");
  }

  /// widget销毁，基于dispose实现
  void onDestroy() {
    log("onDestroy:${getWidgetName()}");
  }

  /// 进入前台
  /// 子页面暂不支持
  void onForeground() {
    log("onForeground:${getWidgetName()}");
    track("onForeground");
  }

  /// 进入后台
  /// 子页面暂不支持
  void onBackground() {
    log("onBackground:${getWidgetName()}");
    track("onBackground");
  }

  ///---------------------------------页面必须重写的方法----------------------------

  ///设置内容Widget
  Widget buildWidget(BuildContext context);
}
